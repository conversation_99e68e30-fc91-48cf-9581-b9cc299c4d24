from typing import Optional

from app.model import Agent
from app.schema.message import <PERSON><PERSON><PERSON>, AgentType
from pkg.exception import HttpException
from pkg.message import ErrorMessage
from beanie.operators import Eq, Or


class AgentService:
    def __init__(self):
        pass

    @staticmethod
    async def find_all_by_project(project: Optional[str], mode: Optional[AgentMode] = None):
        system_query = [Eq(Agent.project, None), Eq(Agent.is_active, True)]
        project_query = [Eq(Agent.project, project), Eq(Agent.is_active, True)]

        if mode is not None:
            system_query.append(Eq(Agent.mode, mode))
            project_query.append(Eq(Agent.mode, mode))

        system_agents = await Agent.find(*system_query).to_list()
        project_agents = await Agent.find(*project_query).to_list()
        existed_project_agents = map(lambda x: x.code, project_agents)
        project_agents.extend(filter(lambda x: x.code not in existed_project_agents, system_agents))
        return project_agents

    @staticmethod
    async def find_master_agent_by_project_and_mode(project: Optional[str], mode: Optional[AgentMode] = None):
        """
        Find the master agent for a specific project and mode.

        Args:
            project: Project ID (None for system-scoped agents)
            mode: Agent mode (route/coordinate) for master agents

        Returns:
            Master agent configuration or None if not found
        """
        query_conditions = [
            Eq(Agent.type, AgentType.MASTER),
            Eq(Agent.is_active, True)
        ]

        if mode is not None:
            query_conditions.append(Eq(Agent.mode, mode))

        # First try to find project-specific master agent
        if project:
            project_query = query_conditions + [Eq(Agent.project, project)]
            master_agent = await Agent.find_one(*project_query)
            if master_agent:
                return master_agent

        # Fallback to system-scoped master agent
        system_query = query_conditions + [Eq(Agent.project, None)]
        return await Agent.find_one(*system_query)

    @staticmethod
    async def find_all_worker_agents_by_project(project: Optional[str]):
        """
        Find all worker agents for a specific project.

        Args:
            project: Project ID (None for system-scoped agents)

        Returns:
            List of worker agent configurations
        """
        system_agents = await Agent.find(
            Eq(Agent.project, None),
            Eq(Agent.is_active, True),
            Eq(Agent.type, AgentType.WORKER)
        ).to_list()

        project_agents = await Agent.find(
            Eq(Agent.project, project),
            Eq(Agent.is_active, True),
            Eq(Agent.type, AgentType.WORKER)
        ).to_list()

        # Merge project and system agents, prioritizing project-specific ones
        existed_project_agents = map(lambda x: x.code, project_agents)
        project_agents.extend(filter(lambda x: x.code not in existed_project_agents, system_agents))
        return project_agents

    @staticmethod
    async def find_all_agents_for_master(project: Optional[str], mode: Optional[AgentMode] = None):
        """
        Find master agent and all worker agents for creating a master agent team.

        Args:
            project: Project ID (None for system-scoped agents)
            mode: Agent mode (route/coordinate) for master agents

        Returns:
            List containing master agent (if found) and all worker agents
        """
        agents = []

        # Get mode-specific master agent
        master_agent = await AgentService.find_master_agent_by_project_and_mode(project, mode)
        if master_agent:
            agents.append(master_agent)

        # Get all worker agents
        worker_agents = await AgentService.find_all_worker_agents_by_project(project)
        agents.extend(worker_agents)

        return agents

    @staticmethod
    async def find_by_code(agent_code: str, version: str = None, project: Optional[str] = None, mode: Optional[AgentMode] = None):
        if version:
            query_conditions = [Agent.code == agent_code, Agent.version == version,
                               Or(Eq(Agent.project == project, Eq(Agent.project, None)))]
            if mode is not None:
                query_conditions.append(Agent.mode == mode)

            agent = await Agent.find_one(*query_conditions)
            if not agent:
                raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS,
                                              {Agent.code: agent_code, Agent.version: version})
            return agent

        query_conditions = [Agent.code == agent_code, Agent.is_active == True,
                           Agent.project == project or Agent.project is None]
        if mode is not None:
            query_conditions.append(Agent.mode == mode)

        agent = await Agent.find_one(*query_conditions)
        if not agent:
            raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS, {Agent.code: agent_code})
        return agent

    @staticmethod
    def __next_version(version: str, major_version: bool = False) -> str:
        try:
            major, minor = version.split('.')
            if major_version:
                major = int(major) + 1
                minor = 0
            else:
                minor = int(minor) + 1
            return f"{major}.{minor}"
        except (ValueError, IndexError):
            return "0.1"

    @staticmethod
    def __version_comparable_key(agent: Agent):
        version = agent.version or "0.1"
        try:
            major, minor = version.split('.')
            return int(major), int(minor)
        except (ValueError, IndexError):
            return 0, 1

    async def update_by_code(self, agent_code: str, update_payload: dict):
        project = update_payload.get("project", None)
        versions = await Agent.find(Agent.code == agent_code,
                                    Agent.project == project).to_list()

        latest_version = max(versions, key=self.__version_comparable_key)
        if not latest_version:
            latest_version = self.find_by_code(agent_code)
            latest_version.version = "0.0"

        lastest_version_dump = latest_version.model_dump()
        del lastest_version_dump["id"]
        next_version = Agent(**lastest_version_dump)

        next_version.system_prompt = update_payload["system_prompt"]
        next_version.additional_prompt = update_payload["additional_prompt"]
        next_version.model_id = update_payload["model_id"]
        next_version.project = project

        next_version.version = self.__next_version(latest_version.version)
        next_version.is_active = True
        await Agent.save(next_version)

        await Agent.find(Agent.code == agent_code, Agent.project == update_payload.get("project", None),
                         Agent.id != next_version.id).update({
            "$set": {Agent.is_active: False}
        })

        return next_version

    @staticmethod
    async def find_all_versions_by_code(self, agent_code: str, project: Optional[str] = None):
        agents = await Agent.find(Agent.code == agent_code, Agent.project == project).to_list()

        if not agents:
            raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS, {"code": agent_code})

        return sorted(agents, key=self.__version_comparable_key, reverse=True)
