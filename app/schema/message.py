from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import Field

from app.constant import AgentCode, ReferenceType
from pkg.base import BaseModel


class AgentMode(str, Enum):
    ROUTE = "route"
    COORDINATE = "coordinate"


class AgentType(str, Enum):
    MASTER = "master"
    WORKER = "worker"


class ReferenceSchema(BaseModel):
    id: str = Field(..., description="The id of the reference")
    type: ReferenceType = Field(..., description="The type of the reference")
    name: str = Field(..., description="The name of the reference")
    chunk_id: Optional[str] = Field(None, description="The chunk id of the reference")
    content: Optional[str] = Field("", description="The content of the reference")


class MessageSchema(BaseModel):
    id: str = Field(..., description="The id of the message")
    content: str = Field(..., description="The content of the message")
    reasoning_content: Optional[str] = Field("", description="The reasoning content of the message")
    steps: list[Dict[str, Any]] = Field([], description="The steps of the message")
    references: list[ReferenceSchema] = Field([], description="The references of the message")
    is_bot: bool = Field(True, description="Whether the message is from a bot or not")
    prompt_tokens: int = Field(0, description="The number of tokens in the prompt")
    completion_tokens: int = Field(0, description="The number of tokens in the completion")
    user_id: Optional[str] = Field(
        None, description="The user id of the message. If is bot message, it'll set to run_id"
    )
    reply_to: Optional[str] = Field(None, description="The id of the message this message is replying to")
    conversation_id: str = Field(..., description="The conversation id of the message")
    created_at: Optional[datetime]
    updated_at: Optional[datetime]


class SendMessageRequest(BaseModel):
    content: str = Field(..., description="The content of the message")
    agent_code: str = Field(AgentCode.Master, description="The agent code of the message")
    mode: AgentMode = Field(AgentMode.COORDINATE, description="The mode for master agent: 'route' or 'coordinate'")
    sse: bool = Field(True, description="Whether to use server-sent events or not")
    references: Optional[List[ReferenceSchema]] = Field([], description="The references of the message, if any")


class MessageContentUpdateRequest(BaseModel):
    content: str = Field(..., description="The new content of the message")


class MessageEvent(BaseModel):
    id: Optional[str] = None
    event: str
    conversation_id: str

    content: Optional[str] = None
    reasoning_content: Optional[str] = None
    step: Optional[Dict[str, Any]] = None
    is_bot: Optional[bool] = True
    reply_to: Optional[str] = None
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    conversation_name: Optional[str] = None
    user_id: Optional[str] = None


class MessageEventError(MessageEvent):
    id: Optional[str] = None
    conversation_id: Optional[str] = None
    error: str
