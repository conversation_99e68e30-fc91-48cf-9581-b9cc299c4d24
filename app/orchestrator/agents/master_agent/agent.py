from textwrap import dedent
from typing import Any, Dict, List, Optional

from agno.storage.mongodb import MongoDbStorage
from agno.team.team import Team
from agno.tools.reasoning import ReasoningTools

from app.config.get_config import get_config
from app.constant import AgentCode
from app.factories.chat_factory import create_chat_model
from app.factories.knowledge_base_factory import create_knowledge_base
from app.model import AgnoSession
from app.orchestrator.agents.hlr_agent.agent import HLRAgent
from app.orchestrator.agents.ur_agent.agent import URAgent
from app.orchestrator.agents.usecase_agent.agent import UseCaseAgent
from app.orchestrator.agents.screen_agent.agent import ScreenAgent
from app.schema import AgentSchema
from app.schema.message import AgentMode
from app.utils.config_helper import Agent<PERSON>onfigH<PERSON>per

from .prompts import (
    MASTER_AGENT_DESCRIPTION,
    MASTER_AGENT_ROLE,
    MASTER_AGENT_SYSTEM_INSTRUCTION,
    MASTER_AGENT_COORDINATE_INSTRUCTION,
    MASTER_AGENT_ROUTE_INSTRUCTION
)


class MasterAgent:
    def __init__(
        self,
        agent_id=None,
        user_id=None,
        session_id=None,
        debug_mode: bool = True,
        all_agents_config: List[AgentSchema] = None,
        file_ids: Optional[List[str]] = None,
        mode: Optional[AgentMode] = None,
    ):
        self._team_agent = None
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.all_agents_config = all_agents_config or []
        self.file_ids = file_ids
        self.mode = mode or AgentMode.COORDINATE

        # Process and organize agent configs for easy access
        self.agents_by_code = self._organize_agents_by_code()

        # Processed master config for easy access
        self.master_config = self._process_master_config()

    def _safe_get(self, obj, attr: str, default=None):
        """Safely get attribute from object with fallback"""
        try:
            return getattr(obj, attr, default) if obj else default
        except AttributeError:
            return default

    def _safe_get_nested(self, obj, path: str, default=None):
        """Safely get nested attribute using dot notation (e.g., 'prompt_template.instructions')"""
        try:
            result = obj
            for attr in path.split("."):
                result = getattr(result, attr, None)
                if result is None:
                    return default
            return result if result is not None else default
        except AttributeError:
            return default

    def _organize_agents_by_code(self) -> Dict[str, Any]:
        """Organize agent configs by their code for easy lookup"""
        agents_dict = {}

        for agent_config in self.all_agents_config:
            try:
                agent_code = self._safe_get(agent_config, "code", "")
                if agent_code:
                    agents_dict[agent_code] = agent_config
                    agent_name = self._safe_get(agent_config, "name", "Unknown")
                    print(f"Registered agent: {agent_code} - {agent_name}")
            except Exception as e:
                print(f"Error processing agent config: {e}")
                continue

        return agents_dict

    def _process_master_config(self) -> Dict[str, Any]:
        """Process master config and extract commonly used values"""
        master_config = self.agents_by_code.get(AgentCode.Master)

        # Select system prompt based on mode
        if self.mode == AgentMode.ROUTE:
            default_system_prompt = MASTER_AGENT_ROUTE_INSTRUCTION
        else:
            default_system_prompt = MASTER_AGENT_COORDINATE_INSTRUCTION

        if not master_config:
            return {
                "model_id": "gpt-4o",
                "system_prompt": default_system_prompt,
                "additional_prompt": "",
                "name": "Master Agent",
                "role": MASTER_AGENT_ROLE,
                "description": MASTER_AGENT_DESCRIPTION,
            }

        return {
            "model_id": self._safe_get(master_config, "model_id", "gpt-4o"),
            "system_prompt": self._safe_get(
                master_config, "system_prompt", default_system_prompt
            ),
            "additional_prompt": self._safe_get(master_config, "additional_prompt", ""),
            "name": self._safe_get(master_config, "name", "Master Agent"),
            "role": self._safe_get(master_config, "role", "Coordinator"),
            "tools": self._safe_get(master_config, "tools", []),
            "description": self._safe_get(master_config, "description", ""),
        }

    @property
    def ur_agent(self):
        ur_agent_config = self.agents_by_code.get(AgentCode.UserRequirement)
        return URAgent(
            agent_id=AgentCode.UserRequirement,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=ur_agent_config,
            file_ids=self.file_ids,
        ).agent

    @property
    def hlr_agent(self):
        hlr_agent_config = self.agents_by_code.get(AgentCode.HighLevelRequirement)
        return HLRAgent(
            agent_id=AgentCode.HighLevelRequirement,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=hlr_agent_config,
            file_ids=self.file_ids,
        ).agent

    @property
    def usecase_agent(self):
        usecase_agent_config = self.agents_by_code.get(AgentCode.UseCase)
        return UseCaseAgent(
            agent_id=AgentCode.UseCase,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=usecase_agent_config,
            file_ids=self.file_ids,
        ).agent
    
    @property
    def screen_agent(self):
        screen_agent_config = self.agents_by_code.get(AgentCode.Screen)
        return ScreenAgent(
            agent_id=AgentCode.Screen,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=screen_agent_config,
            file_ids=self.file_ids,
        ).agent

    @property
    def agent(self):
        if not self._team_agent:
            config = get_config().chat_model_config
            knowledge_base = create_knowledge_base()

            mongo_config = get_config().mongo_config

            storage = MongoDbStorage(
                collection_name=AgnoSession.Settings.name,
                db_name=mongo_config.database_name,
                db_url=mongo_config.url,
            )
            print("Additional prompt", self.master_config["additional_prompt"])

            # Base agent parameters
            team_params = {
                "team_id": AgentCode.Master,
                "name": self.master_config["name"],
                "description": dedent(self.master_config["description"]),
                "model": create_chat_model(config, model_id="gpt-o4-mini"),
                "members": [self.ur_agent, self.hlr_agent, self.usecase_agent, self.screen_agent],
                "tools": [ReasoningTools(add_instructions=True)],
                "instructions": dedent(
                    AgentConfigHelper.merge_instructions(
                        self.master_config["system_prompt"], self.master_config["additional_prompt"]
                    )
                ),
                "markdown": True,
                "show_members_responses": True,
                "share_member_interactions": True,
                "enable_agentic_context": True,
                "add_datetime_to_instructions": True,
                "debug_mode": True,
                "user_id": self.user_id,
                "session_id": self.session_id,
                "storage": storage,
                "add_history_to_messages": True,
                "num_history_runs": 5,
                "mode": self.mode.value,
                "knowledge": knowledge_base,
                "search_knowledge": True,
                # "enable_user_memories": True,
            }

            # Add knowledge parameters if file_ids is provided
            if self.file_ids:
                team_params.update({"knowledge_filters": {"document_id": self.file_ids}})

            # Use processed master config (much cleaner!)
            self._team_agent = Team(**team_params)
        return self._team_agent
