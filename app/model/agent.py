from datetime import datetime
from typing import Optional

from beanie import Insert, <PERSON>lace, Save<PERSON><PERSON><PERSON>, Update, before_event

from .base import Base
from app.enum import AgentStatus
from app.schema.message import AgentMode


class Agent(Base):
    class Settings:
        name = "agents"

    name: str
    code: str  # Remove unique constraint from here
    role: str
    description: str = ""
    model_id: str = ""
    system_prompt: str = ""
    additional_prompt: str = ""
    tools: list[str] = []
    project: Optional[str] = None  # If None -> System scoped agent
    version: str = "0.1"
    is_active: bool = True
    status: AgentStatus = AgentStatus.DRAFT  # New status field with default DRAFT
    mode: Optional[AgentMode] = None  # Mode for master agent: 'route' or 'coordinate'
    created_at: Optional[datetime] = datetime.now()
    updated_at: Optional[datetime] = datetime.now()

    @before_event(Insert)
    def set_created_at(self):
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    @before_event(Update, SaveChang<PERSON>, Replace)
    def set_updated_at(self):
        self.updated_at = datetime.now()
